import pandas as pd

# Read first CSV with semicolon delimiter
df_2025 = pd.read_csv('2025_unique.csv', delimiter=';')

# Read second CSV with comma delimiter and rename columns
df_vlaams = pd.read_csv('laadpunten_public.csv', delimiter=',')
columns_to_keep = [
    'FID', 'laadpunt_teller', 'uniek_identificatienummer', 'uitbater',
    'toegankelijkheid', 'kw', 'snelheid', 'adres', 'postcode',
    'gemeente', 'latitude', 'longitude'
]
df_vlaams = df_vlaams[columns_to_keep]

df_vlaams = df_vlaams.rename(columns={
    'uniek_identificatienummer': 'EMI3_EAN',
    'uitbater': 'Name',
    'toegankelijkheid': 'ReportingElectricTypeNL',
    'gemeente': 'City',
    'postcode': 'Zip'
})

# Get counts from both DataFrames by zip code
vlaams_zip_counts = df_vlaams['Zip'].value_counts().reset_index()
vlaams_zip_counts.columns = ['Zip Code', 'Vlaams Count']

df_2025_zip_counts = df_2025['Zip'].value_counts().reset_index()
df_2025_zip_counts.columns = ['Zip Code', '2025 Count']

# Merge the counts into a comparative table
comparison_table = pd.merge(
    vlaams_zip_counts,
    df_2025_zip_counts,
    on='Zip Code',
    how='outer'
)

df_2025.head()
df_vlaams.head()
comparison_table


