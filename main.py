import pandas as pd
import numpy as np
from math import radians, sin, cos, sqrt, atan2

# Read first CSV with semicolon delimiter
df_2025 = pd.read_csv('2025_unique.csv', delimiter=';')

# Read second CSV with comma delimiter and rename columns
df_vlaams = pd.read_csv('laadpunten_public.csv', delimiter=',')
columns_to_keep = [
    'FID', 'laadpunt_teller', 'uniek_identificatienummer', 'uitbater',
    'toegankelijkheid', 'kw', 'snelheid', 'adres', 'postcode',
    'gemeente', 'latitude', 'longitude'
]
df_vlaams = df_vlaams[columns_to_keep]

df_vlaams = df_vlaams.rename(columns={
    'uniek_identificatienummer': 'EMI3_EAN',
    'uitbater': 'Name',
    'toegankelijkheid': 'ReportingElectricTypeNL',
    'gemeente': 'City',
    'postcode': 'Zip'
})



# Get counts from both DataFrames by zip code
vlaams_zip_counts = df_vlaams['Zip'].value_counts().reset_index()
vlaams_zip_counts.columns = ['Zip Code', 'Vlaams Count']

df_postal_codes = pd.read_csv('postal-codes-belgium.csv', delimiter=';')

# Filter for 'Région flamande'
vlaams_postal_codes = df_postal_codes[df_postal_codes['Région name (French)'] == 'Région flamande']['Postal Code']

# First filter df_2025 to only include Vlaams postal codes
df_2025_vlaams = df_2025[df_2025['Zip'].isin(vlaams_postal_codes)]

# Function to calculate distance between two points using Haversine formula
def haversine_distance(lat1, lon1, lat2, lon2):
    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    distance = 6371000 * c  # Earth radius in meters
    return distance

# Function to find if a station has a match within 20 meters
def find_geo_match(row):
    # First try to match by EMI3_EAN if available
    if 'EMI3_EAN' in df_2025_vlaams.columns and 'EMI3_EAN' in df_vlaams.columns:
        if pd.notna(row['EMI3_EAN']) and row['EMI3_EAN'] in df_vlaams['EMI3_EAN'].values:
            return True
    
    # Filter by same zip code first to reduce computation
    same_zip = df_vlaams[df_vlaams['Zip'] == row['Zip']]
    
    # If no stations with same zip, return False
    if len(same_zip) == 0:
        return False
    
    # Check distances to all stations with same zip
    for _, station in same_zip.iterrows():
        distance = haversine_distance(
            row['latitude'], row['longitude'],
            station['latitude'], station['longitude']
        )
        if distance <= 20:  # 20 meters radius
            return True
    
    return False

# Apply the function to each row
df_2025_vlaams['in_vlaams'] = df_2025_vlaams.apply(find_geo_match, axis=1)

# Count matches and non-matches
match_counts = df_2025_vlaams['in_vlaams'].value_counts()
print("\nMatching results:")
print(f"Stations found in both datasets: {match_counts.get(True, 0)}")
print(f"Stations only in 2025 dataset: {match_counts.get(False, 0)}")
